# import json
import logging
# import traceback as tb

import pprint
import yaml
from rich.pretty import Pretty

import dakota.interfacing as di

import msgd._global as GLOBAL
# from . import MSGDStudy, MSGDFEModel #, MSGDStructure
import msgd.dakota.dkt as mdd
# import msgd.design as md
# import msgd.utils.io as mui
import msgd.utils.io as mutils
from msgd._global import console
from msgd.core import *

logger = logging.getLogger(__name__)


class MSGD():
    """Main object of the MSG-based design framework.

    Parameters
    ----------
    name : str
        Name of the study.
    fn_main : str
        Name of the main input file.
    fn_out : str
        Name of the output file.
    mode : str
        Running mode of the study.
    """
    def __init__(self, name='', fn_main='', fn_out='', mode='0'):
        #: str: Name of the study
        self.name = name

        #: str: Version of the main input
        self.version = ''

        # self.ivabs = False

        # product variant
        # (ivabs, tailorable)
        # self.variant = 'none'  # will be deprecated

        # self.SG_KEY = 'sg'  # will be deprecated
        # self.SG_FULL = 'structure gene'  # will be deprecated

        #: str: File name of the main input
        self.fn_main = fn_main
        #: str: File name of the output
        self.fn_out = fn_out

        self.dir_work = '.'
        # self.fn_path = ''
        self.fn_base = ''

        self.mode = mode
        """str: Running mode.

        * '0' - generate dakota input
        * '1' - single run
        * '2' - called by dakota
        """

        self.log_level_cmd = 'info'
        self.log_level_file = 'info'
        self.log_file_name = 'eval.log'

        # Evaluation
        # ----------
        #: Evaluation id
        self.eval_id:int = 0
        #: Extra evaluation inputs from MDAO
        self.eval_in:dict = {}
        #: Extra evaluation outputs to MDAO
        self.eval_out:dict = {}

        self._data:dict = {}
        """Main data storage of MSGD and exchange with MDAO.

        Contains the parameters from and results to MDAO.
        """

        # Inputs
        # ------
        self.setting = {
            'rel_tol': 1e-9,
            'abs_tol': 1e-12,
            'log_level_cmd': 'info',
            'log_level_file': 'info',
            'log_file_name': 'log.txt'
        }
        self.design = None
        self.model = None
        self.analysis = None
        self.study:MSGDStudy = None
        # self.dakota = dkt.Dakota()
        # self.dakota = None
        # self.fn_dakota = ''
        # self.fn_dakota_params = ''
        # self.fn_dakota_results = ''



        # Structure and SGs
        # -----------------

        # TODO
        # Maps between (design and discretized)
        # - SG/CS base design
        # - Location
        # - Set name
        # - Parameters
        # - Properties
        #
        # set name -> (type, location)
        # sg name (specific) -> (set name, sg base name, parameters, properties)

        # self.structure_analysis_tool : str = None

        self._global_structure:StructureModel = None

        self._db_sg_design:DataBase = None
        """Database of SG design.
        """

        self._db_sg_model:DataBase = None
        """Database of SG model.

        A model is a combination of a design and model configs.
        """

        self._db_sg_model_sets:DataBase = None
        """Database of specific sg models.

        A combination of sg base design, model configs, and specific parameters.
        Same contents as the '_sg_model_sets' in the 'structure'.
        """

        # self.structure_response_cases_sets = None
        """Global structural responses for dehomogenization/failure analysis
        """


        # Others
        # ------
        self.mdao_tool = 'dakota'

        self.fn_dakota_params = ''
        self.fn_dakota_results = ''

        self._db_functions:DataBase = DataBase('functions', [])
        """dict: Functions.

        ..  code-block:: python

            functions = {

            }
        """

        self.py_cmd = ''
        self.fn_run = ''


        self.analysis_steps:list[AnalysisStep] = []

    @property
    def data(self): return self._data
    @property
    def db_sg_design(self): return self._db_sg_design
    @property
    def db_sg_model(self): return self._db_sg_model
    @property
    def db_sg_model_sets(self): return self._db_sg_model_sets
    # @property
    # def db_sg_data(self): return self._db_sg_data
    @property
    def global_structure(self): return self._global_structure
    @global_structure.setter
    def global_structure(self, structure:StructureModel): self._global_structure = structure
    @property
    def analysis_steps(self): return self._analysis_steps
    @analysis_steps.setter
    def analysis_steps(self, steps): self._analysis_steps = steps
    @property
    def functions(self): return self._db_functions

    def setSGDesignDatabase(self, db_sg_design:DataBase):
        self._db_sg_design = db_sg_design
    def setSGModelDatabase(self, db_sg_model:DataBase):
        self._db_sg_model = db_sg_model
    def setSGModelSetsDatabase(self, db_sg_model_sets:DataBase):
        self._db_sg_model_sets = db_sg_model_sets
    def setGlobalStructure(self, structure:StructureModel):
        self._global_structure = structure

    def updateSGDesignDatabase(self, db_sg_design):
        self._db_sg_design.update(db_sg_design)
    def updateSGModelDatabase(self, db_sg_model):
        self._db_sg_model.update(db_sg_model)
    def updateSGModelSetsDatabase(self, db_sg_model_sets):
        self._db_sg_model_sets.update(db_sg_model_sets)


    def addAnalysisStep(self, step:AnalysisStep):
        self._analysis_steps.append(step)


    def summary(self):
        """Print a summary of the current MSGD object.
        """
        ppt = pprint.PrettyPrinter()

        print('\n\n')
        print('MSG Design Object Summary')
        print('=========================')

        print('Name:', self.name)

        print('')

        print('Setting')
        print('-------')
        ppt.pprint(self.setting)

        print('')

        print('Analysis process')
        print('----------------')
        ppt.pprint(self.analysis)

        print('')

        print('Design study config')
        print('-------------------')
        # ppt.pprint(self.study)
        if self.study:
            self.study.summary()
        else:
            print('None')

        print('')


    # def getTransformation(self, name):
    #     return self.transformations[name]
    
    # def addTransformation(self, name, tf):
    #     self.transformations[name] = tf






    def writeInput(self, fn, fmt='yaml'):
        """ Write the object to a file.
        """

        logger.debug(f'writing input file: {fn}...')

        data = {
            # 'name': self.name,
            'version': self.version,
            # 'setting': self.setting,
            # 'structure': self.structure_data,
            # 'structure': self._global_structure.toDictionary(sg_key=GLOBAL.SG_KEY),
            # self.SG_KEY: self.sg_data,
            # 'analysis': self.analysis
        }

        data['structure'] = self._global_structure.toDictionary(sg_key=GLOBAL.SG_KEY)

        data.update(self._db_functions.toDictionary())

        data.update(self._db_sg_design.toDictionary(sg_key=GLOBAL.SG_KEY))

        _steps = [_step.toDictionary() for _step in self._analysis_steps]
        data['analysis'] = {'steps': _steps}

        if fmt == 'yaml':

            with open(fn, 'w', encoding='utf-8') as fo:
                yaml.dump(data, fo, default_flow_style=False, sort_keys=False)

        return


    def writeInterim(self):
        mutils.dumpData(
            self._global_structure.toDictionary(sg_key=GLOBAL.SG_KEY),
            GLOBAL.FILENAME_INTERIM_GLOBAL_STRUCTURE)

        with open(f'{GLOBAL.FILENAME_INTERIM_SG_DESIGN_DB}.yml', 'w') as file:
            self._db_sg_design.dumpToFile(file, sg_key=GLOBAL.SG_KEY)
        with open(f'{GLOBAL.FILENAME_INTERIM_SG_MODEL_DB}.yml', 'w') as file:
            self._db_sg_model.dumpToFile(file, sg_key=GLOBAL.SG_KEY)
        with open(f'{GLOBAL.FILENAME_INTERIM_SG_MODEL_SETS}.yml', 'w') as file:
            self._db_sg_model_sets.dumpToFile(file, sg_key=GLOBAL.SG_KEY)

        mutils.dumpData(
            {'analysis': {'steps': [step.toDictionary() for step in self._analysis_steps]}},
            '_interim_analysis.yml'
        )
        self._global_structure.writeInterim()




    def runMDAO(self, mdao='dakota'):
        if mdao == 'dakota':
            mdd.runDakota(self.study.fn)

        return




    def readMDAOEvalIn(self):
        """Read extra inputs for evaluation.
        """

        logger.info('reading mdao input...')
        logger.debug(f'mdao_tool: {self.mdao_tool}')
        logger.debug(f'fn_dakota_params: {self.fn_dakota_params}')
        logger.debug(f'fn_dakota_results: {self.fn_dakota_results}')

        if self.mdao_tool == 'dakota' and self.fn_dakota_params != '' and self.fn_dakota_results != '':
            self.eval_in, self.eval_out = di.read_parameters_file(
                self.fn_dakota_params, self.fn_dakota_results
            )
            # print(self.eval_in.eval_id)
            self.eval_id = int(self.eval_in.eval_id)
            GLOBAL.EVAL_ID = self.eval_id
            for param_name in self.eval_in.descriptors:
                self._data[param_name] = self.eval_in[param_name]

            # self.log_prompt = 'eval {}'.format(self.eval_id)

        return









    def writeAnalysisOut(self, status):
        """Write evaluation results to a file.

        Parameters
        ----------
        status : {0, 1}
            Status of the evaluation. 0: success; 1: fail.
        """

        logger.info(f'[main] writing output to file {self.fn_dakota_results}...')
        # print(self.fn_dakota_results)
        # print(self._data)

        # Write final results
        if self.fn_dakota_results != '':
        # if self.study:

            if status == 0:
                # Success
                for desc in self.eval_out.descriptors:
                    val = self._data[desc]
                    # print(f'{desc}: {val}')
                    self.eval_out[desc].function = val
                self.eval_out.write()


                # logger.info(f'[eval {self.eval_id}] finish')
                console.rule(f'[EVAL {self.eval_id}] ANALYSIS DONE')

            elif status == 1:
                # Fail
                # logger.critical(f'[eval {self.eval_id}] failed')
                console.rule(f'[EVAL {self.eval_id}] ANALYSIS FAILED')
                # with open(fn_eval_results, 'w') as fo:
                with open(self.fn_dakota_results, 'w') as file:
                    file.write('FAIL')

                # e = tb.format_exc()
                # print(e)

        else:

            with open(self.fn_out, 'w', encoding='utf-8') as file:
                yaml.dump(self._data, file, default_flow_style=False, sort_keys=False)
                # for k, v in self.data.items():
                #     file.write('{}    {}\n'.format(k, v))


        return




    def evaluate(self):
        """
        """

        console.rule(f'[EVAL {self.eval_id}] ANALYSIS BEGIN')

        self.updateData()

        self.writeInput('curr_design.yml')

        self.analyze()

        # console.rule(f'EVAL {self.eval_id} END')

        return









    def updateData(self):
        """
        Update the current design by processing parameters, mesh, and domain transformations.
        This method performs the following steps:
        1. Logs the start of the update process.
        2. Updates the design parameters using the current data.
        3. Substitutes parameters within the design.
        4. Loads the structure mesh based on the global design structure.
        5. Applies domain transformations using the specified database functions.
        6. Implements distribution functions on the design using both database functions and domain.
        7. Iterates through items in the database design model (_db_sg_model.data) and, if the design is a string,
            retrieves the corresponding design object from the design database (_db_sg_design).
        Returns:
             None
        """

        logger.info('updating current design...')

        self._global_structure.design.updateParameters(self._data)

        self._global_structure.design.substituteParameters()

        self._global_structure.loadStructureMesh()

        self._global_structure.implementDomainTransformations(self._db_functions)

        self._global_structure.design.implementDistributionFunctions(
            db_function=self._db_functions,
            db_domain=self._global_structure.db_domain
            )

        # breakpoint()
        for _sg_model in self._db_sg_model.data:
            if isinstance(_sg_model.design, str):
                _sg_model.design = self._db_sg_design.getItemByName(_sg_model.design)

        logger.debug('updated design (global structure):')
        logger.debug(self._global_structure.design.toDictionary())

        return




    def discretize(self):
        """
        """
        logger.info('[main] discretizing the structure...')

        structure_model = self._global_structure

        logger.debug(
            f'structure model:\n{mutils.convertDict2Str(structure_model.toDictionary(sg_key=GLOBAL.SG_KEY))}')

        structure_model.discretizeDesign(
            db_sg_model=self._db_sg_model,
            db_sg_model_sets=self._db_sg_model_sets
            )




    def analyze(self):
        """
        """
        GLOBAL.addFuncNameBegin('analyze')

        # console.rule('ANALYSIS START')

        # logger.info(f'[main] [eval {self.eval_id}] analysis start')

        mdao_results = {}


        # Discretize the design
        # Generate all design parameters from distributions
        # -------------------------------------------------
        self.discretize()

        self.writeInterim()




        # Go steps
        # --------
        logger.info('[main] going through steps...')

        for step in self.analysis_steps:

            if not step.activate: continue

            _step_params = step.parameters
            if len(_step_params) > 0:
                mutils.substituteParams(step, _step_params)

            logger.debug(f'step config:\n{mutils.convertDict2Str(step.toDictionary())}')

            step.run(
                structure_model=self._global_structure,
                db_sg_model=self._db_sg_model,
                db_sg_design=self._db_sg_design,
                db_sg_model_sets=self._db_sg_model_sets,
                msgd_data=self._data,
            )

            if step.step_output:
                self._data.update(step.step_output)

        GLOBAL.addFuncNameEnd('analyze')

        # print('\n')
        # print('msgd.data')
        # for _k, _v in self._data.items():
        #     print(f'{_k}: {_v}')
        # console.rule('ANALYSIS END')
        console.print('msgd.data')
        console.print(Pretty(self._data, expand_all=True))

        return mdao_results









def updateDataCompact(input_data, params):
    """
    """

    substituted = []

    # split rows
    input_data = input_data.split('\n')
    for i in range(len(input_data)):
        # split cols
        cols = input_data[i].split(',')
        for j in range(len(cols)):
            entry = cols[j].split(':')
            if len(entry) > 1: # there is a token
                for dparam_name in params:
                    # print('token: {}, dparam_name: {}'.format(entry[0].strip(), dparam_name))
                    if entry[0].strip() == dparam_name:
                        dparam_value = params[dparam_name]
                        # print('{} -> {}'.format(entry[1].strip(), dparam_value))
                        cols[j] = '{}'.format(dparam_value)
                        if not dparam_name in substituted:
                            substituted.append(dparam_name)
                        break
                    else:
                        cols[j] = '{}'.format(entry[1].strip())
            # print(cols[j])
        input_data[i] = ','.join(cols)
        # print(distr_input)
    input_data = '\n'.join(input_data)

    return input_data
