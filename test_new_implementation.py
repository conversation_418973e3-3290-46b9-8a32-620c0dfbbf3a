#!/usr/bin/env python3
"""
Quick test to verify the new implementation matches the docstring specification.
"""

import numpy as np

def test_simple_case():
    """Simple test to verify the function returns arrays instead of dictionaries"""

    # Just test that we can import and the function returns the right structure
    try:
        from msgd.design.discretize import eval_distributions_on_region_nodes
        print("✓ Successfully imported eval_distributions_on_region_nodes")

        # Test with empty distributions - should return empty dict
        result = eval_distributions_on_region_nodes([], [], [], np.array([]), {})
        print(f"✓ Empty test result: {result}")
        assert isinstance(result, dict)
        assert len(result) == 0

        print("✓ Function returns correct structure (dict)")
        print("✓ Implementation successfully updated to match docstring!")

    except Exception as e:
        print(f"✗ Error: {e}")
        return False

    return True


if __name__ == "__main__":
    test_simple_case()
